apiVersion: apps/v1
kind: Deployment
metadata:
  name: prod-zeus-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zeus
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  minReadySeconds: 5
  template:
    metadata:
      labels:
        app: zeus
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: main
          image: REGISTRY/NAMESPACE/IMAGE:TAG
          readinessProbe:
            httpGet:
              path: /actuator/readiness
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5
          ports:
            - containerPort: 8080
          resources:
            requests:
              cpu: "2"
              memory: "4Gi"
            limits:
              cpu: "4"
              memory: "8Gi"
          volumeMounts:
            - name: config-volume
              mountPath: /app/application.properties
              subPath: application.properties
      volumes:
        - name: config-volume
          configMap:
            name: zeus-config
