apiVersion: apps/v1
kind: Deployment
metadata:
  name: zeus-deployment
spec:
  template:
    spec:
      containers:
        - name: main
          resources:
            requests:
              cpu: "0.5"
              memory: "500Mi"
            limits:
              cpu: "4"
              memory: "8Gi"
      volumes:
        - name: config-volume
          configMap:
            name: dev-zeus-config