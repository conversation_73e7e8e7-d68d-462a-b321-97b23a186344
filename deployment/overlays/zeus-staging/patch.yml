apiVersion: apps/v1
kind: Deployment
metadata:
  name: zeus-deployment
spec:
  template:
    metadata:
      labels:
        armsPilotAutoEnable: "on"
        armsPilotCreateAppName: "staging-zeus"
    spec:
      containers:
        - name: main
          env:
            - name: OTEL_ENVIRONMENT
              value: "staging"
      volumes:
        - name: config-volume
          configMap:
            name: staging-zeus-config