#!/bin/bash

# 检查是否传入了 JAR 包路径参数
if [ -z "$1" ]; then
  echo "Usage: ./start.sh <path_to_jar>"
  exit 1
fi

JAR_PATH=$1

# 检查文件是否存在
if [ ! -f "$JAR_PATH" ]; then
  echo "Error: File not found at path: $JAR_PATH"
  exit 1
fi

# 根据环境变量设置不同的端点
if [ "$OTEL_ENVIRONMENT" = "prod" ]; then
  echo "Using production OpenTelemetry endpoints"
  # 生产环境配置
  # 硅谷-internal
#  TRACES_ENDPOINT="http://tracing-analysis-dc-usw-internal.aliyuncs.com/adapt_fa6f9pxqjp@ee54678be765e11_fa6f9pxqjp@53df7ad2afe8301/api/otlp/traces"
#  METRICS_ENDPOINT="http://tracing-analysis-dc-usw-internal.aliyuncs.com/adapt_fa6f9pxqjp@ee54678be765e11_fa6f9pxqjp@53df7ad2afe8301/api/otlp/metrics"

  # 杭州-外网
  TRACES_ENDPOINT="http://tracing-analysis-dc-hz.aliyuncs.com/adapt_fa6f9pxqjp@bf4a38a192f4a74_fa6f9pxqjp@53df7ad2afe8301/api/otlp/traces"
  METRICS_ENDPOINT="http://tracing-analysis-dc-hz.aliyuncs.com/adapt_fa6f9pxqjp@bf4a38a192f4a74_fa6f9pxqjp@53df7ad2afe8301/api/otlp/metrics"
else
  echo "Using non-production OpenTelemetry endpoints"
  # 非生产环境配置
  # 香港-internal
#  TRACES_ENDPOINT="http://tracing-analysis-dc-hk-internal.aliyuncs.com/adapt_fa6f9pxqjp@bf4a38a192f4a74_fa6f9pxqjp@53df7ad2afe8301/api/otlp/traces"
#  METRICS_ENDPOINT="http://tracing-analysis-dc-hk-internal.aliyuncs.com/adapt_fa6f9pxqjp@bf4a38a192f4a74_fa6f9pxqjp@53df7ad2afe8301/api/otlp/metrics"

  # 杭州-外网
  TRACES_ENDPOINT="http://tracing-analysis-dc-hz.aliyuncs.com/adapt_fa6f9pxqjp@bf4a38a192f4a74_fa6f9pxqjp@53df7ad2afe8301/api/otlp/traces"
  METRICS_ENDPOINT="http://tracing-analysis-dc-hz.aliyuncs.com/adapt_fa6f9pxqjp@bf4a38a192f4a74_fa6f9pxqjp@53df7ad2afe8301/api/otlp/metrics"
fi

# 定义OOM发生时执行的命令
OOM_COMMAND="/bin/bash /app/sys/handle_oom.sh %p"

# ==============================================
# 启动应用
# openTelemetry通过javaagent自动插桩
# todo -XX:NativeMemoryTracking=summary 不用后可以下线
# ==============================================
exec java -javaagent:./opentelemetry-javaagent.jar \
  -Dotel.resource.attributes=service.name=$OTEL_SERVICE_NAME,service.version=$OTEL_SERVICE_VERSION,deployment.environment=$OTEL_ENVIRONMENT \
  -Dotel.exporter.otlp.protocol=http/protobuf \
  -Dotel.exporter.otlp.traces.endpoint=$TRACES_ENDPOINT \
  -Dotel.exporter.otlp.metrics.endpoint=$METRICS_ENDPOINT \
  -Dotel.logs.exporter=none \
  -Duser.timezone=UTC \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=$HEAPDUMP_PATH \
  -XX:OnOutOfMemoryError="$OOM_COMMAND" \
  -XX:NativeMemoryTracking=summary \
  -jar "$JAR_PATH" \
  --spring.config.location=file:/app/application.properties
